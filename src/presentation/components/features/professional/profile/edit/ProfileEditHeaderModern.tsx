import React, { ComponentProps, useState } from "react";
import { User, MapPin, Edit3, Save, Crown, Stethoscope, X } from "lucide-react";
import { professionnels_titre_enum } from "@/domain/models/enums";
import BaseInfoEditForm from "./BaseInfoEditForm";
import { motion } from "framer-motion";
import { BaseInfoSectionData } from "@/presentation/hooks/professional/use-professional-base-info-section-handler";
import { useSectionForm } from "@/presentation/hooks/professional/use-section-form";
import { baseInfoSchema } from "@/shared/schemas/ProfessionalProfileSchemas";
import useProfessionalProfile from "@/presentation/hooks/professional/use-professional-profile.ts";

/**
 * Interface pour les propriétés du composant ProfileEditHeaderModern
 */
type ProfileEditHeaderModernProps = ComponentProps<"div"> & {
  /** Titre du professionnel (Dr., Pr., etc.) */
  titre: professionnels_titre_enum;
  /** Nom de famille du professionnel */
  nom: string;
  /** Prénom du professionnel */
  prenom: string;
  /** Liste des spécialités du professionnel */
  specialites: Array<{ nom_specialite: string }>;
  /** Fokontany (localisation) du professionnel */
  fokontany: string;
  /** Adresse complète du professionnel */
  adresse: string;
  /** Fonction pour mettre à jour les informations de base */
  onBaseInfoUpdate?: (
    baseInfo: Partial<BaseInfoSectionData>
  ) => Promise<boolean>;
  /** Indique si une mise à jour est en cours */
};

/**
 * Composant d'en-tête moderne pour l'édition du profil professionnel
 *
 * Ce composant utilise un design cohérent avec les autres sections CRUD
 * tout en maintenant une hiérarchie visuelle distinctive pour l'en-tête.
 * Il intègre les patterns de couleurs et d'espacement des autres sections.
 *
 * @example
 * ```tsx
 * <ProfileEditHeaderModern
 *   titre="Dr."
 *   nom="Dupont"
 *   prenom="Jean"
 *   specialites={[{ nom_specialite: "Cardiologie" }]}
 *   fokontany="Antananarivo"
 *   adresse="123 Rue de la Paix"
 *   isEditing={false}
 *   onEditToggle={() => setIsEditing(!isEditing)}
 *   professionalId={1}
 * />
 * ```
 */
const ProfileEditHeaderModern: React.FC<ProfileEditHeaderModernProps> = ({
  titre,
  nom,
  prenom,
  specialites,
  fokontany,
  adresse,
  onBaseInfoUpdate,
  className,
  ...props
}) => {
  const [isEditingBaseInfo, setIsEditingBaseInfo] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Gestion du formulaire avec react-hook-form et validation Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useSectionForm({
    schema: baseInfoSchema,
    defaultValues: {
      nom: nom || "",
      prenom: prenom || "",
      titre: titre || professionnels_titre_enum.PR,
    },
    onSave: async (data) => await onBaseInfoUpdate(data),
    sectionName: "informations de base",
  });

  /**
   * Gère l'annulation de l'édition des informations de base
   */
  const handleBaseInfoCancel = () => {
    setIsEditingBaseInfo(false);
  };

  /**
   * Gère le clic sur le bouton d'édition des informations de base
   */
  const handleEditBaseInfo = () => {
    setIsEditingBaseInfo(true);
  };

  return (
    <div className={className} {...props}>
      {/* En-tête principal avec style cohérent mais distinctif */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 mb-6 overflow-hidden"
      >
        {/* Bande de couleur subtile pour maintenir la hiérarchie */}
        <div className="h-2 bg-gradient-to-r from-meddoc-fonce to-meddoc-primary"></div>

        <div className="p-6">
          <div className="flex justify-between items-start gap-6">
            <div className="flex-1 min-w-0">
              {isEditingBaseInfo ? (
                <div className="flex items-start gap-4 mb-6">
                  <div className="rounded-lg p-4 border w-full">
                    <motion.form
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      id="edit-professional-form-header"
                      onSubmit={handleSubmit(async (data) => {
                        setIsSaving(true);
                        await onBaseInfoUpdate(data);
                        setIsSaving(false);
                        handleBaseInfoCancel();
                      })}
                    >
                      <BaseInfoEditForm register={register} />
                    </motion.form>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleBaseInfoCancel}
                      disabled={isSaving}
                      className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
                      title="Annuler"
                    >
                      <X className="h-4 w-4" />
                    </button>
                    <button
                      type="submit"
                      form="edit-professional-form-header"
                      onClick={handleEditBaseInfo}
                      disabled={isSaving}
                      className="px-3 py-1.5 text-sm bg-meddoc-primary text-white hover:bg-meddoc-primary/90 rounded-lg transition-colors disabled:opacity-50 flex items-center gap-2"
                      title="Sauvegarder"
                    >
                      <Save className="h-4 w-4" />
                      {isSaving ? "Sauvegarde..." : "Sauvegarder"}
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex items-start gap-4 mb-6">
                    <div className="p-3 bg-meddoc-primary/10 dark:bg-meddoc-primary/20 rounded-lg flex-shrink-0">
                      <Crown className="h-8 w-8 text-meddoc-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1 min-w-0">
                          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight break-words mb-2">
                            {titre} {nom} {prenom}
                          </h1>
                          <div className="flex items-center gap-2">
                            <span className="px-3 py-1 bg-meddoc-primary/10 dark:bg-meddoc-primary/20 text-meddoc-primary rounded-full text-sm font-medium">
                              Professionnel de santé
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={handleEditBaseInfo}
                      className="p-2 text-gray-500 dark:text-gray-400 hover:text-meddoc-primary hover:bg-meddoc-primary/10 dark:hover:bg-meddoc-primary/20 rounded-lg transition-colors"
                      title={`Modifier mon nom`}
                    >
                      <Edit3 className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Spécialités et localisation avec style cohérent */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Spécialités */}
                    {specialites && specialites.length > 0 && (
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <Stethoscope className="h-4 w-4 text-meddoc-primary" />
                          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Spécialités
                          </h3>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {specialites.map((specialite, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm border border-blue-200 dark:border-blue-700"
                            >
                              {specialite.nom_specialite}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Localisation */}
                    {(fokontany || adresse) && (
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <MapPin className="h-4 w-4 text-meddoc-primary" />
                          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Localisation
                          </h3>
                        </div>
                        <div className="text-gray-700 dark:text-gray-300">
                          <p className="text-sm leading-relaxed">
                            {fokontany && adresse
                              ? `${fokontany}, ${adresse}`
                              : fokontany || adresse}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileEditHeaderModern;
