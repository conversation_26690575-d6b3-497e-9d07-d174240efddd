import { useCallback, useEffect } from "react";
import { He<PERSON><PERSON> } from "react-helmet-async";
import { motion } from "framer-motion";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { ListeAssurances } from "@/domain/models/AssuranceProfessionnel.ts";
import {
  ProfileEditHeaderModern,
  ProfileEditPhotoModern,
  PresentationEditSection,
  ProfessionalInformationEdit,
  ServicesEditSection,
  EstablishmentCRUDSection,
  PaymentAndInsuranceCRUDSection,
  KeywordsCRUDSection,
  LanguagesCRUDSection,
  CabinetImagesCRUDSection,
  DiplomasCRUDSection,
  ExperiencesCRUDSection,
  PublicationsCRUDSection,
  SpecialtiesCRUDSection,
  ContactCRUDSection,
} from "@/presentation/components/features/professional/profile/edit";
import useProfessionalProfile from "@/presentation/hooks/professional/use-professional-profile.ts";
import { useProfessionalProfileHandlers } from "@/presentation/hooks/professional/use-professional-profile-handlers";

import { PhotoTypeEnum } from "@/domain/models/enums/photo_type_enum.ts";
import { Photo } from "@/domain/models/Photo";
import { DiplomeProfessionnel, ExperienceProfessionnel } from "@/domain/models";
import { PublicationProfessionnel } from "@/domain/models/PublicationProfessionnel";
import { useAppSelector } from "@/presentation/hooks/redux.ts";
import { ProfessionalProfileData } from "@/domain/DTOS/ProfessionalDTO.ts";

const ProfessionalProfile = () => {
  const { id } = useAppSelector((state) => state.authentification.user);

  const {
    profileData,
    isLoading,
    error: profileError,
    fetchProfileData,
    updateBaseInfoSection,
    updatePresentationSection,
    updateServices,
    motClesActions,
    languagesActions,
    updateContactSection,
    updateProfessionalInformationSection,
    setProfileData,
    updateGeolocation,
  } = useProfessionalProfile(id);

  // Utilisation du hook principal pour tous les handlers
  const handlers = useProfessionalProfileHandlers();

  // Handlers personnalisés pour les photos avec rafraîchissement
  const handlePhotoUpload = useCallback(
    async (file: File) => {
      const success = await handlers.handlePhotoUpload(file);
      if (success) {
        // Rafraîchir les données du profil après l'upload
        await fetchProfileData(true);
      }
      return success;
    },
    [handlers, fetchProfileData]
  );

  const handlePhotoRemove = useCallback(async () => {
    const success = await handlers.handlePhotoRemove();
    if (success) {
      // Rafraîchir les données du profil après la suppression
      await fetchProfileData(true);
    }
    return success;
  }, [handlers, fetchProfileData]);

  // Handlers personnalisés pour les images du cabinet avec rafraîchissement
  const handleCabinetImagesAdd = useCallback(
    async (files: File[]) => {
      const success = await handlers.handleAddImages(files);
      if (success) {
        // Rafraîchir les données du profil après l'upload
        await fetchProfileData(true);
      }
      return success;
    },
    [handlers, fetchProfileData]
  );

  const handleCabinetImageRemove = useCallback(
    async (imageId: number) => {
      const success = await handlers.handleRemoveImage(imageId);
      if (success) {
        // Rafraîchir les données du profil après la suppression
        await fetchProfileData(true);
      }
      return success;
    },
    [handlers, fetchProfileData]
  );

  // Handlers personnalisés pour les assurances avec rafraîchissement
  const handleAddInsurance = useCallback(
    async (nom: string, availableInsurances?: ListeAssurances[]) => {
      const success = await handlers.handleAddInsurance(
        nom,
        availableInsurances
      );
      if (success) {
        // Rafraîchir les données du profil après l'ajout
        await fetchProfileData(true);
      }
      return success;
    },
    [handlers, fetchProfileData]
  );

  const handleRemoveInsurance = useCallback(
    async (insuranceId: number) => {
      const success = await handlers.handleRemoveInsurance(insuranceId);
      if (success) {
        // Rafraîchir les données du profil après la suppression
        await fetchProfileData(true);
      }
      return success;
    },
    [handlers, fetchProfileData]
  );

  const handleGeolocationChange = useCallback(
    async (value: string) => {
      const professionalId = profileData.baseInfo.id || 0;
      if (!professionalId) {
        return false;
      }
      return await updateGeolocation(professionalId, value);
    },
    [handlers, fetchProfileData]
  );

  useEffect(() => {
    if (id) {
      fetchProfileData();
    }
  }, [id, fetchProfileData]);

  const updateDiploma = useCallback(
    (updatedDiplomas: DiplomeProfessionnel[]) => {
      setProfileData({
        ...profileData,
        diplomas: updatedDiplomas,
      });
    },
    [profileData, setProfileData]
  );

  const updateExperience = useCallback(
    (updatedExperiences: ExperienceProfessionnel[]) => {
      setProfileData({
        ...profileData,
        experiences: updatedExperiences,
      });
    },
    [profileData, setProfileData]
  );

  const updatePublications = useCallback(
    (updatedPublications: PublicationProfessionnel[]) => {
      setProfileData({
        ...profileData,
        publications: updatedPublications,
      });
    },
    [profileData, setProfileData]
  );

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 justify-center items-center min-h-screen">
        <LoadingSpinner className="h-auto" />
        <p>Chargement des donnees</p>
      </div>
    );
  }

  if (profileError) {
    return null;
  }

  if (!profileData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-gray-600">Aucune donnée trouvée</div>
      </div>
    );
  }

  const pageTitle = `${profileData.baseInfo.titre} ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom} - ${profileData.specialities[0]?.nom_specialite || "Médecin"} à ${profileData.baseInfo.fokontany}`;
  const pageDescription = `${profileData.baseInfo.titre} ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom}, ${profileData.specialities.map((s) => s.nom_specialite).join(", ")} à ${profileData.baseInfo.fokontany}. ${profileData.baseInfo.presentation_generale.substring(0, 160)}...`;
  const keywords = `${profileData.specialities.map((s) => s.nom_specialite).join(", ")}, médecin ${profileData.baseInfo.fokontany}, ${profileData.baseInfo.titre} ${profileData.baseInfo.nom}`;

  const profileImage = profileData?.photos?.[0]?.path || "";

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="keywords" content={keywords} />
        {/* Pour le partage sur Facebook */}
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:image" content={profileImage} />
      </Helmet>

      <div className="w-full mx-auto px-2 sm:px-3 lg:px-4 py-4">
        {/* En-tête du profil avec animation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <ProfileEditHeaderModern
            titre={profileData.baseInfo.titre}
            nom={profileData.baseInfo.nom}
            prenom={profileData.baseInfo.prenom}
            specialites={profileData.specialities}
            fokontany={profileData.baseInfo.fokontany}
            adresse={profileData.baseInfo.adresse}
            onBaseInfoUpdate={(baseInfo) =>
              updateBaseInfoSection(profileData.baseInfo.id, baseInfo)
            }
          />
        </motion.div>

        {/* <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6"> */}
        <div className="flex flex-col gap-4 lg:gap-6">
          <div className="flex gap-4">
            {/* Colonne de gauche - Photo et informations principales */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              className="flex flex-col sticky top-2 bg-red-500"
            >
              {/* Photo de profil */}
              <ProfileEditPhotoModern
                photoUrl={profileImage}
                fullName={`${profileData.baseInfo.titre} ${profileData.baseInfo.nom} ${profileData.baseInfo.prenom}`}
                onPhotoUpload={handlePhotoUpload}
                onPhotoRemove={handlePhotoRemove}
                maxSizeMB={5}
              />

              {/* Informations de contact */}
              <ContactCRUDSection
                email={profileData?.userData?.email || ""}
                telephone={profileData?.contact?.[0] || null}
                adresse={profileData.baseInfo?.adresse || ""}
                fokontany={profileData.baseInfo?.fokontany || ""}
                infoAcces={profileData.baseInfo?.informations_acces || ""}
                onSave={(data) =>
                  updateContactSection(
                    data,
                    profileData?.contact?.[0]?.id || 0,
                    profileData.baseInfo.utilisateur_id,
                    profileData.baseInfo.id
                  )
                }
              />
            </motion.div>

            {/* Colonne de droite - Informations détaillées */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              className="lg:col-span-2 space-y-4"
            >
              {/* Présentation */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <PresentationEditSection
                  presentationGenerale={
                    profileData.baseInfo.presentation_generale || ""
                  }
                  onSave={(data) => updatePresentationSection(data)}
                />
              </motion.div>

              {/* Spécialités */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <SpecialtiesCRUDSection
                  specialties={profileData.specialities}
                  onAddSpecialty={async (ids) => {
                    const ok = await handlers.handleAddSpecialty(ids);
                    if (ok) {
                      await fetchProfileData(true);
                    }
                    return ok;
                  }}
                  onDeleteSpecialty={async (specId) => {
                    const ok = await handlers.handleDeleteSpeciality(specId);
                    if (ok) {
                      await fetchProfileData(true);
                    }
                    return ok;
                  }}
                />
              </motion.div>

              {/* Informations professionnelles */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <ProfessionalInformationEdit
                  numeroOrdre={profileData.baseInfo.numero_ordre || ""}
                  raisonSociale={profileData.baseInfo.raison_sociale || ""}
                  nif={profileData.baseInfo.nif || ""}
                  stat={profileData.baseInfo.stat || ""}
                  onSave={(data) =>
                    updateProfessionalInformationSection(
                      profileData.baseInfo.id || 0,
                      data
                    )
                  }
                />
              </motion.div>

              {/* Services */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <ServicesEditSection
                  typesConsultation={profileData.baseInfo.types_consultation}
                  nouveauPatientAcceptes={
                    profileData.baseInfo.nouveau_patient_acceptes
                  }
                  onSave={(data) =>
                    updateServices(profileData.baseInfo.id || 0, data)
                  }
                />
              </motion.div>

              {/* Informations de l'établissement */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <EstablishmentCRUDSection
                  etablishmentData={profileData.etablishments[0]}
                  geolocation={profileData.baseInfo.geolocalisation || ""}
                  handleGeolocationChange={handleGeolocationChange}
                  onSave={handlers.handleSaveEstablishment}
                />
              </motion.div>

              {/* Diplômes */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <DiplomasCRUDSection
                  diplomas={profileData.diplomas}
                  professionalId={profileData.baseInfo.id || 0}
                  updateDiplomaState={updateDiploma}
                />
              </motion.div>

              {/* Expériences */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <ExperiencesCRUDSection
                  experiences={profileData.experiences}
                  professionalId={profileData.baseInfo.id || 0}
                  updateExperienceState={updateExperience}
                />
              </motion.div>

              {/* Publications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <PublicationsCRUDSection
                  publications={profileData.publications}
                  updatePublicationState={updatePublications}
                  onAddPublication={handlers.handleAddPublication}
                  onUpdatePublication={handlers.handleUpdatePublication}
                  onDeletePublication={handlers.handleDeletePublication}
                />
              </motion.div>

              {/* Langues parlées */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.65 }}
              >
                <LanguagesCRUDSection
                  languages={profileData.languages}
                  onAddLanguages={async (languageNames) => {
                    const professionalId = profileData?.baseInfo.id || 0;
                    if (professionalId && languagesActions) {
                      const actions = languagesActions();

                      // Utiliser la fonction d'ajout multiple optimisée
                      const result = await actions.addLanguages(
                        professionalId,
                        languageNames
                      );

                      setProfileData({
                        ...profileData,
                        languages: result,
                      });
                      return result !== null;
                    }
                    return false;
                  }}
                  onDeleteLanguage={async (languageId) => {
                    if (languagesActions) {
                      const actions = languagesActions();
                      const success = await actions.deleteLanguage(languageId);

                      // languagesActions gère déjà le rafraîchissement
                      return success;
                    }
                    return false;
                  }}
                />
              </motion.div>

              {/* Modes de paiement et assurances */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <PaymentAndInsuranceCRUDSection
                  paymentMethods={profileData.paymentMethods}
                  insurances={profileData.insurances}
                  onSavePaymentMethods={(payments) => {
                    const result = handlers.handleSavePaymentMethods(payments);
                    setProfileData({
                      ...profileData,
                      paymentMethods: payments,
                    });
                    return result;
                  }}
                  onAddInsurance={handleAddInsurance}
                  onRemoveInsurance={handleRemoveInsurance}
                />
              </motion.div>

              {/* Mots-clés */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <KeywordsCRUDSection
                  keywords={profileData.keywords}
                  onAddKeywords={async (keywordIds) => {
                    const professionalId = profileData?.baseInfo.id || 0;
                    if (professionalId && motClesActions) {
                      const actions = motClesActions();
                      let allSuccess = true;

                      // Utiliser la fonction d'ajout multiple optimisée
                      const result = await actions.addMotClesByIds(
                        professionalId,
                        keywordIds
                      );
                      allSuccess = result;

                      return allSuccess;
                    }
                    return false;
                  }}
                  onDeleteKeyword={async (keywordId) => {
                    if (motClesActions) {
                      const actions = motClesActions();
                      const success = await actions.deleteMotCle(keywordId);

                      // motClesActions gère déjà le rafraîchissement
                      return success;
                    }
                    return false;
                  }}
                  onDeleteKeywordSilent={async (keywordId) => {
                    if (motClesActions) {
                      const actions = motClesActions();
                      const success =
                        await actions.deleteMotClesSilent(keywordId);
                      return success;
                    }
                    return false;
                  }}
                  onRefreshData={async () => {
                    await fetchProfileData(true);
                  }}
                />
              </motion.div>

              {/* Galerie d'images du cabinet */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.9 }}
              >
                <CabinetImagesCRUDSection
                  images={(profileData.photos as Photo[]).filter(
                    (photo) => photo.type === PhotoTypeEnum.PRESENTATION
                  )}
                  onAddImages={handleCabinetImagesAdd}
                  onRemoveImage={handleCabinetImageRemove}
                />
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfessionalProfile;
